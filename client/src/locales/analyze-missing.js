import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取语言包文件
const enUS = JSON.parse(fs.readFileSync(path.join(__dirname, 'en-US.json'), 'utf8'));
const zhCN = JSON.parse(fs.readFileSync(path.join(__dirname, 'zh-CN.json'), 'utf8'));
const zhHK = JSON.parse(fs.readFileSync(path.join(__dirname, 'zh-HK.json'), 'utf8'));

// 递归获取所有键路径
function getAllKeys(obj, prefix = '') {
  const keys = [];
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys.push(...getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
}

// 检查键是否存在于对象中
function hasKey(obj, keyPath) {
  const keys = keyPath.split('.');
  let current = obj;
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return false;
    }
  }
  return true;
}

// 获取英文语言包的所有键
const enKeys = getAllKeys(enUS);

// 找出简体中文缺失的键
const zhCNMissing = enKeys.filter(key => !hasKey(zhCN, key));

// 找出繁体中文缺失的键
const zhHKMissing = enKeys.filter(key => !hasKey(zhHK, key));

console.log('=== 英文语言包分析结果 ===\n');
console.log(`英文语言包总键数: ${enKeys.length}`);
console.log(`简体中文缺失键数: ${zhCNMissing.length}`);
console.log(`繁体中文缺失键数: ${zhHKMissing.length}`);

console.log('\n=== 简体中文缺失的键 ===');
zhCNMissing.forEach(key => {
  console.log(`${key}: "${getValueByPath(enUS, key)}"`);
});

console.log('\n=== 繁体中文缺失的键 ===');
zhHKMissing.forEach(key => {
  console.log(`${key}: "${getValueByPath(enUS, key)}"`);
});

// 获取对象中指定路径的值
function getValueByPath(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

// 生成缺失键的JSON结构
function generateMissingStructure(keys, sourceObj) {
  const result = {};
  keys.forEach(keyPath => {
    const keys = keyPath.split('.');
    let current = result;
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = getValueByPath(sourceObj, keyPath);
  });
  return result;
}

// 生成简体中文缺失结构
const zhCNMissingStructure = generateMissingStructure(zhCNMissing, enUS);
console.log('\n=== 简体中文缺失结构 (JSON) ===');
console.log(JSON.stringify(zhCNMissingStructure, null, 2));

// 生成繁体中文缺失结构
const zhHKMissingStructure = generateMissingStructure(zhHKMissing, enUS);
console.log('\n=== 繁体中文缺失结构 (JSON) ===');
console.log(JSON.stringify(zhHKMissingStructure, null, 2));
