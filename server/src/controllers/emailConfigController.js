import { EmailConfig } from '../models/EmailConfig.js';
import { EmailService } from '../utils/emailService.js';
import { 
  successResponse, 
  errorResponse, 
  validationErrorResponse,
  notFoundResponse 
} from '../utils/response.js';

/**
 * 获取所有邮箱配置
 */
export async function getEmailConfigs(req, res) {
  try {
    const { page = 1, limit = 10 } = req.query;

    console.log('控制器接收到的查询参数:', req.query);

    const configs = await EmailConfig.findAll({
      page: parseInt(page),
      limit: parseInt(limit)
    });

    console.log('查询到的配置数量:', configs.length);
    console.log('原始配置数据:', configs);

    // 返回安全的配置信息（不包含密码）
    const safeConfigs = configs.map(config => config.toSafeJSON());

    console.log('安全配置数据:', safeConfigs);

    return successResponse(res, safeConfigs, '获取邮箱配置列表成功');
  } catch (error) {
    console.error('获取邮箱配置列表失败:', error);
    return errorResponse(res, '获取邮箱配置列表失败', 500);
  }
}

/**
 * 根据ID获取邮箱配置
 */
export async function getEmailConfigById(req, res) {
  try {
    const { id } = req.params;
    
    const config = await EmailConfig.findById(id);
    if (!config) {
      return notFoundResponse(res, '邮箱配置不存在');
    }
    
    return successResponse(res, config.toSafeJSON(), '获取邮箱配置成功');
  } catch (error) {
    console.error('获取邮箱配置失败:', error);
    return errorResponse(res, '获取邮箱配置失败', 500);
  }
}

/**
 * 创建邮箱配置
 */
export async function createEmailConfig(req, res) {
  try {
    const {
      smtp_host,
      smtp_port,
      smtp_secure,
      smtp_user,
      smtp_pass,
      from_name,
      from_email
    } = req.body;

    // 创建配置
    const newConfig = await EmailConfig.create({
      smtp_host,
      smtp_port: parseInt(smtp_port),
      smtp_secure: Boolean(smtp_secure),
      smtp_user,
      smtp_pass,
      from_name,
      from_email
    });

    return successResponse(res, newConfig.toSafeJSON(), '邮箱配置创建成功', 201);
  } catch (error) {
    console.error('创建邮箱配置失败:', error);
    return errorResponse(res, '创建邮箱配置失败', 500);
  }
}

/**
 * 更新邮箱配置
 */
export async function updateEmailConfig(req, res) {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 检查配置是否存在
    const existingConfig = await EmailConfig.findById(id);
    if (!existingConfig) {
      return notFoundResponse(res, '邮箱配置不存在');
    }

    // 处理数据类型
    if (updateData.smtp_port) {
      updateData.smtp_port = parseInt(updateData.smtp_port);
    }
    if (updateData.smtp_secure !== undefined) {
      updateData.smtp_secure = Boolean(updateData.smtp_secure);
    }

    // 更新配置
    const updatedConfig = await EmailConfig.update(id, updateData);
    
    return successResponse(res, updatedConfig.toSafeJSON(), '邮箱配置更新成功');
  } catch (error) {
    console.error('更新邮箱配置失败:', error);
    return errorResponse(res, '更新邮箱配置失败', 500);
  }
}

/**
 * 删除邮箱配置
 */
export async function deleteEmailConfig(req, res) {
  try {
    const { id } = req.params;

    // 检查配置是否存在
    const existingConfig = await EmailConfig.findById(id);
    if (!existingConfig) {
      return notFoundResponse(res, '邮箱配置不存在');
    }

    // 删除配置
    const deleted = await EmailConfig.delete(id);
    if (!deleted) {
      return errorResponse(res, '删除邮箱配置失败', 500);
    }

    return successResponse(res, null, '邮箱配置删除成功');
  } catch (error) {
    console.error('删除邮箱配置失败:', error);
    return errorResponse(res, '删除邮箱配置失败', 500);
  }
}

/**
 * 激活邮箱配置
 */
export async function activateEmailConfig(req, res) {
  try {
    const { id } = req.params;

    // 检查配置是否存在
    const existingConfig = await EmailConfig.findById(id);
    if (!existingConfig) {
      return notFoundResponse(res, '邮箱配置不存在');
    }

    // 激活配置
    const activated = await EmailConfig.activate(id);
    if (!activated) {
      return errorResponse(res, '激活邮箱配置失败', 500);
    }

    return successResponse(res, null, '邮箱配置激活成功');
  } catch (error) {
    console.error('激活邮箱配置失败:', error);
    return errorResponse(res, '激活邮箱配置失败', 500);
  }
}

/**
 * 测试邮箱配置连接
 */
export async function testEmailConfig(req, res) {
  try {
    const { id } = req.params;
    const { test_email } = req.body;

    // 获取配置
    const config = await EmailConfig.findById(id);
    if (!config) {
      return notFoundResponse(res, '邮箱配置不存在');
    }

    // 测试连接
    const testResult = await EmailService.testConfig({
      smtp_host: config.smtp_host,
      smtp_port: config.smtp_port,
      smtp_secure: config.smtp_secure,
      smtp_user: config.smtp_user,
      smtp_pass: config.smtp_pass
    });

    if (!testResult) {
      return errorResponse(res, '邮箱配置连接测试失败，请检查配置信息', 400);
    }

    // 如果提供了测试邮箱，发送测试邮件
    if (test_email) {
      try {
        const emailService = new EmailService();
        // 临时设置配置
        emailService.config = config;
        emailService.transporter = require('nodemailer').createTransport({
          host: config.smtp_host,
          port: config.smtp_port,
          secure: config.smtp_secure,
          auth: {
            user: config.smtp_user,
            pass: config.smtp_pass
          },
          tls: {
            rejectUnauthorized: false
          }
        });

        await emailService.sendTestEmail(test_email);
        return successResponse(res, null, '郵箱配置測試成功，測試郵件已發送');
      } catch (emailError) {
        console.error('发送测试邮件失败:', emailError);
        return successResponse(res, null, '郵箱配置連接成功，但發送測試郵件失敗');
      }
    }

    return successResponse(res, null, '郵箱配置連接測試成功');
  } catch (error) {
    console.error('测试邮箱配置失败:', error);
    return errorResponse(res, '測試郵箱配置失敗', 500);
  }
}

/**
 * 获取当前激活的邮箱配置
 */
export async function getActiveEmailConfig(req, res) {
  try {
    const config = await EmailConfig.getActiveConfig();
    
    if (!config) {
      return notFoundResponse(res, '未找到激活的邮箱配置');
    }
    
    return successResponse(res, config.toSafeJSON(), '获取激活邮箱配置成功');
  } catch (error) {
    console.error('获取激活邮箱配置失败:', error);
    return errorResponse(res, '获取激活邮箱配置失败', 500);
  }
}
